import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';

class IconTextfield extends StatelessWidget {
  const IconTextfield(
      {super.key,
      required this.controller,
      required this.hintText,
      required this.prefixIcon,
      this.suffixIcon,
      this.suffixPadding,
      this.onChanged,
      this.hintStyle,
      this.keyboardType,
      this.obscureText,
      this.inputFormatters,
      this.textStyle});

  final TextEditingController controller;
  final String hintText;
  final Widget prefixIcon;
  final Widget? suffixIcon;
  final EdgeInsets? suffixPadding;
  final Function(String)? onChanged;
  final TextStyle? hintStyle;
  final TextInputType? keyboardType;
  final bool? obscureText;
  final List<TextInputFormatter>? inputFormatters;
  final TextStyle? textStyle;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 60.gw,
      child: TextField(
        keyboardType: keyboardType,
        controller: controller,
        obscureText: obscureText ?? false,
        inputFormatters: inputFormatters,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: hintStyle ?? context.textTheme.highlight,
          contentPadding: EdgeInsets.only(
            right: 16.gw,
            top: 16.gh,
            bottom: 16.gh,
          ),
          prefixIconConstraints: BoxConstraints(
            minWidth: 76.gw, // 56.gw (icon) + 20.gw (spacing)
            maxWidth: 76.gw,
          ),
          suffixIcon: suffixPadding != null ? Padding(padding: suffixPadding!, child: suffixIcon) : suffixIcon,
          prefixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 56.gw,
                height: 56.gw,
                decoration: BoxDecoration(
                  color: context.colorTheme.iconBgA,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.gw),
                    bottomLeft: Radius.circular(12.gw),
                  ),
                ),
                child: prefixIcon,
              ),
              SizedBox(width: 20.gw), // Spacing between icon and text
            ],
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(12.gw)),
            borderSide: BorderSide(
              color: context.colorTheme.borderA,
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(12.gw)),
            borderSide: BorderSide(
              color: context.colorTheme.borderA,
              width: 1,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(12.gw)),
            borderSide: BorderSide(
              color: context.colorTheme.borderA,
              width: 1,
            ),
          ),
        ),
        onChanged: onChanged,
        style: textStyle ?? context.textTheme.regular.fs16,
      ),
    );
  }
}
